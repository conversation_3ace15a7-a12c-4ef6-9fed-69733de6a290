// scripts/migrate.ts - Actualizado para incluir todas las migraciones
import { readFile } from "node:fs/promises";
import { join } from "node:path";
import { pool } from "../src/db.js";
import { logger } from "../src/logger.js";

// Lista de archivos de migración en orden
const migrationFiles = [
  "001_init.sql",
  "002_seed_minima.sql",
  "003_admin_improvements.sql",
];

async function runMigrations() {
  logger.info("Starting database migrations...");

  try {
    for (const file of migrationFiles) {
      const filePath = join("migrations", file);
      logger.info(`Applying migration: ${file}`);

      try {
        const sql = await readFile(filePath, "utf8");

        // Split SQL statements by semicolon and filter out empty statements
        const statements = sql
          .split(";")
          .map((stmt) => stmt.trim())
          .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"));

        // Execute each statement individually
        for (const statement of statements) {
          if (statement.trim()) {
            try {
              await pool.query(statement);
            } catch (statementError: any) {
              // Log the statement that failed for debugging
              logger.error(
                {
                  error: statementError,
                  statement: statement.substring(0, 200) + "...",
                  file,
                },
                "Failed to execute SQL statement"
              );

              // Re-throw to stop migration process
              throw statementError;
            }
          }
        }

        logger.info(`✅ Migration ${file} completed successfully`);
      } catch (fileError: any) {
        logger.error({ error: fileError, file }, `❌ Migration ${file} failed`);
        throw fileError;
      }
    }

    logger.info("🎉 All migrations completed successfully");
  } catch (error: any) {
    logger.error({ error }, "💥 Migration process failed");
    process.exit(1);
  } finally {
    // Cerrar el pool de conexiones
    await pool.end();
  }
}

// Ejecutar migraciones
runMigrations()
  .then(() => {
    logger.info("Migration process finished");
    process.exit(0);
  })
  .catch((error) => {
    logger.error({ error }, "Migration process failed with unhandled error");
    process.exit(1);
  });
