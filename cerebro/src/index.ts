// src/index.ts - Actualizado con nuevas rutas admin
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import pinoHttp from 'pino-http';
import rateLimit from 'express-rate-limit';
import { env } from './env.js';
import { logger } from './logger.js';
import { checkDatabaseHealth, closeDatabasePool } from './db.js';
import { errorHandler } from './middlewares/errorHandler.js';
import wellKnown from './routes/wellKnown.js';
import jwks from './routes/jwks.js';
import authorize from './routes/authorize.js';
import token from './routes/token.js';
import userinfo from './routes/userinfo.js';
import admin from './routes/admin.js';
import adminAuth from './routes/adminAuth.js';
import profile from './routes/profile.js';

const app = express();

// Middlewares básicos
app.use(helmet());
app.use(express.json());

// CORS - En desarrollo permitimos localhost, en producción será más restrictivo
const corsOptions = {
  origin: env.NODE_ENV === 'development'
    ? ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:8080']
    : false, // En producción, configurar dominios específicos
  credentials: true
};

app.use(cors(corsOptions));
app.use(pinoHttp({ logger }));

// Rate limiting más permisivo para admin en desarrollo
const rateLimitOptions = env.NODE_ENV === 'development'
  ? { windowMs: 60_000, max: 200 }  // Más requests en dev
  : { windowMs: 60_000, max: 120 }; // Límite normal en prod

app.use(rateLimit(rateLimitOptions));

// ============================================
// RUTAS OIDC ESTÁNDAR
// ============================================
app.use('/.well-known', wellKnown);
app.use('/jwks.json', jwks);
app.use('/authorize', authorize);
app.use('/token', token);
app.use('/userinfo', userinfo);
app.use('/user', profile);

// ============================================
// RUTAS ADMIN
// ============================================
app.use('/admin/auth', adminAuth); // Autenticación específica de admin
app.use('/admin', admin);          // CRUD de recursos (requiere auth)

// ============================================
// HEALTH CHECK MEJORADO
// ============================================
app.get('/health', async (req, res) => {
  const dbHealth = await checkDatabaseHealth();

  const health = {
    status: dbHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: env.NODE_ENV,
    services: {
      database: dbHealth ? 'up' : 'down',
      redis: 'up', // TODO: Implementar check de Redis cuando lo uses
    },
    uptime: process.uptime()
  };

  const statusCode = dbHealth ? 200 : 503;
  res.status(statusCode).json(health);
});

// ============================================
// ERROR HANDLING
// ============================================
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'not_found',
    message: `Route ${req.method} ${req.originalUrl} not found`
  });
});

// ============================================
// SERVER STARTUP
// ============================================
const server = app.listen(env.PORT, () => {
  logger.info({
    port: env.PORT,
    environment: env.NODE_ENV,
    issuer: env.OIDC_ISSUER
  }, 'cerebro-idp server started');
});

// ============================================
// GRACEFUL SHUTDOWN
// ============================================
const gracefulShutdown = async (signal: string) => {
  logger.info(`${signal} received, shutting down gracefully`);

  // Cerrar el servidor HTTP
  server.close(async () => {
    logger.info('HTTP server closed');

    try {
      // Cerrar conexiones de base de datos
      await closeDatabasePool();

      // Aquí podrías cerrar otras conexiones (Redis, etc.)

      logger.info('All connections closed, exiting process');
      process.exit(0);
    } catch (error) {
      logger.error({ error }, 'Error during graceful shutdown');
      process.exit(1);
    }
  });

  // Force exit after 10 seconds
  setTimeout(() => {
    logger.error('Forced exit after timeout');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.fatal({ error }, 'Uncaught exception');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.fatal({ reason, promise }, 'Unhandled rejection');
  process.exit(1);
});
