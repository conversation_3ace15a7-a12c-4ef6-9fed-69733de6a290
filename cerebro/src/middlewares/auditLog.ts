// src/middlewares/auditLog.ts
import { Request, Response, NextFunction } from 'express';
import { pool } from '../db.js';
import { logger } from '../logger.js';

export interface AuditLogEntry {
  user_id?: number;
  action: string;
  resource_type: string;
  resource_id?: string;
  details?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
}

export async function createAuditLog(entry: AuditLogEntry): Promise<void> {
  try {
    await pool.query(
      `INSERT INTO audit_log (user_id, action, resource_type, resource_id, details, ip_address, user_agent)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        entry.user_id || null,
        entry.action,
        entry.resource_type,
        entry.resource_id || null,
        entry.details ? JSON.stringify(entry.details) : null,
        entry.ip_address || null,
        entry.user_agent || null
      ]
    );
  } catch (error) {
    logger.error({ error, entry }, 'Failed to create audit log entry');
  }
}

// Middleware para auto-logging de acciones admin
export const auditMiddleware = (action: string, resourceType: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const originalSend = res.send;

    res.send = function(data: any) {
      // Solo logear si la operación fue exitosa (2xx status codes)
      if (res.statusCode >= 200 && res.statusCode < 300) {
        const userId = req.auth?.sub ? parseInt(req.auth.sub) : undefined;
        const resourceId = req.params.id || req.body?.id || req.body?.client_id;

        const auditEntry: AuditLogEntry = {
          user_id: userId,
          action,
          resource_type: resourceType,
          resource_id: resourceId?.toString(),
          details: {
            method: req.method,
            path: req.path,
            query: req.query,
            // No incluir datos sensibles como passwords
            body: sanitizeBody(req.body)
          },
          ip_address: getClientIP(req),
          user_agent: req.get('User-Agent')
        };

        // Crear audit log de forma asíncrona
        createAuditLog(auditEntry).catch(error => {
          logger.error({ error }, 'Audit log creation failed');
        });
      }

      return originalSend.call(this, data);
    };

    next();
  };
};

// Utilidad para obtener la IP real del cliente
function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
    req.headers['x-real-ip'] as string ||
    req.socket.remoteAddress ||
    'unknown'
  );
}

// Sanitizar el body para no logear información sensible
function sanitizeBody(body: any): any {
  if (!body || typeof body !== 'object') return body;

  const sensitiveFields = ['password', 'client_secret', 'token', 'code', 'code_verifier'];
  const sanitized = { ...body };

  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

// Funciones de conveniencia para logging manual
export const auditLog = {
  userCreated: (adminUserId: number, createdUserId: number, userEmail: string, ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'user_created',
      resource_type: 'user',
      resource_id: createdUserId.toString(),
      details: { created_user_email: userEmail },
      ip_address: ip
    }),

  userUpdated: (adminUserId: number, updatedUserId: number, changes: string[], ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'user_updated',
      resource_type: 'user',
      resource_id: updatedUserId.toString(),
      details: { updated_fields: changes },
      ip_address: ip
    }),

  userDeleted: (adminUserId: number, deletedUserId: number, deletedUserEmail: string, ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'user_deleted',
      resource_type: 'user',
      resource_id: deletedUserId.toString(),
      details: { deleted_user_email: deletedUserEmail },
      ip_address: ip
    }),

  clientCreated: (adminUserId: number, clientId: string, ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'client_created',
      resource_type: 'oidc_client',
      resource_id: clientId,
      ip_address: ip
    }),

  clientUpdated: (adminUserId: number, clientId: string, changes: string[], ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'client_updated',
      resource_type: 'oidc_client',
      resource_id: clientId,
      details: { updated_fields: changes },
      ip_address: ip
    }),

  clientDeleted: (adminUserId: number, clientId: string, ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'client_deleted',
      resource_type: 'oidc_client',
      resource_id: clientId,
      ip_address: ip
    }),

  jwkRotated: (adminUserId: number, newKid: string, ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'jwk_rotated',
      resource_type: 'jwk_key',
      resource_id: newKid,
      ip_address: ip
    }),

  adminLogin: (userId: number, email: string, ip?: string, userAgent?: string) =>
    createAuditLog({
      user_id: userId,
      action: 'admin_login',
      resource_type: 'auth_session',
      details: { email },
      ip_address: ip,
      user_agent: userAgent
    }),

  adminLogout: (userId: number, ip?: string) =>
    createAuditLog({
      user_id: userId,
      action: 'admin_logout',
      resource_type: 'auth_session',
      ip_address: ip
    }),

  configUpdated: (adminUserId: number, configKey: string, ip?: string) =>
    createAuditLog({
      user_id: adminUserId,
      action: 'config_updated',
      resource_type: 'system_config',
      resource_id: configKey,
      ip_address: ip
    })
};

export default auditMiddleware;
