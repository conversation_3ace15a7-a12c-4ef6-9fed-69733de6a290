// src/routes/admin.ts
import { Router } from 'express';
import { z } from 'zod';
import { pool } from '../db.js';
import { requireAdmin, requireAuth } from '../middlewares/requireAuth.js';
import { asyncHandler } from '../middlewares/errorHandler.js';
import { getUserRoles, getUserById } from '../services/users.js';
import { getClientById } from '../services/oidc.js';
import argon2 from 'argon2';

const r = Router();

// Middleware para todas las rutas admin
r.use(requireAdmin);

// ============================================
// ENDPOINTS DE USUARIOS
// ============================================

// GET /admin/users - Listar usuarios con paginación
r.get('/users', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const search = req.query.search as string || '';
  const offset = (page - 1) * limit;

  let whereClause = '';
  let params: any[] = [];

  if (search) {
    whereClause = 'WHERE email LIKE ? OR display_name LIKE ?';
    params = [`%${search}%`, `%${search}%`];
  }

  // Obtener usuarios
  const [users]: any[] = await pool.query(
    `SELECT id, email, display_name, created_at, updated_at
     FROM users ${whereClause}
     ORDER BY created_at DESC
     LIMIT ? OFFSET ?`,
    [...params, limit, offset]
  );

  // Obtener total de usuarios para paginación
  const [[countResult]]: any[] = await pool.query(
    `SELECT COUNT(*) as total FROM users ${whereClause}`,
    params
  );

  // Obtener roles para cada usuario
  const usersWithRoles = await Promise.all(
    users.map(async (user: any) => {
      const roles = await getUserRoles(user.id);
      return { ...user, roles };
    })
  );

  res.json({
    users: usersWithRoles,
    pagination: {
      page,
      limit,
      total: countResult.total,
      pages: Math.ceil(countResult.total / limit)
    }
  });
}));

// GET /admin/users/:id - Obtener usuario por ID
r.get('/users/:id', asyncHandler(async (req, res) => {
  const userId = parseInt(req.params.id);
  const user = await getUserById(userId);

  if (!user) {
    return res.status(404).json({ error: 'user_not_found' });
  }

  const roles = await getUserRoles(userId);
  res.json({ ...user, password_hash: undefined, roles });
}));

// POST /admin/users - Crear nuevo usuario
const CreateUserSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  display_name: z.string().optional(),
  roles: z.array(z.string()).default(['CLIENT'])
});

r.post('/users', asyncHandler(async (req, res) => {
  const parsed = CreateUserSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const { email, password, display_name, roles } = parsed.data;

  // Verificar que el email no exista
  const [[existingUser]]: any[] = await pool.query(
    'SELECT id FROM users WHERE email = ? LIMIT 1',
    [email]
  );

  if (existingUser) {
    return res.status(409).json({ error: 'email_already_exists' });
  }

  // Hash de la contraseña
  const password_hash = await argon2.hash(password, { type: argon2.argon2id });

  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();

    // Crear usuario
    const [result]: any = await connection.query(
      'INSERT INTO users (email, password_hash, display_name) VALUES (?, ?, ?)',
      [email, password_hash, display_name || null]
    );

    const userId = result.insertId;

    // Asignar roles
    for (const roleName of roles) {
      const [[role]]: any[] = await connection.query(
        'SELECT id FROM roles WHERE name = ? LIMIT 1',
        [roleName]
      );

      if (role) {
        await connection.query(
          'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
          [userId, role.id]
        );
      }
    }

    await connection.commit();

    const newUser = await getUserById(userId);
    const userRoles = await getUserRoles(userId);

    res.status(201).json({
      ...newUser,
      password_hash: undefined,
      roles: userRoles
    });

  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}));

// PUT /admin/users/:id - Actualizar usuario
const UpdateUserSchema = z.object({
  email: z.string().email().optional(),
  password: z.string().min(8).optional(),
  display_name: z.string().optional(),
  roles: z.array(z.string()).optional()
});

r.put('/users/:id', asyncHandler(async (req, res) => {
  const userId = parseInt(req.params.id);
  const parsed = UpdateUserSchema.safeParse(req.body);

  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const user = await getUserById(userId);
  if (!user) {
    return res.status(404).json({ error: 'user_not_found' });
  }

  const { email, password, display_name, roles } = parsed.data;
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    // Actualizar campos del usuario
    const updates: string[] = [];
    const values: any[] = [];

    if (email !== undefined) {
      // Verificar que el email no exista en otro usuario
      const [[existingUser]]: any[] = await connection.query(
        'SELECT id FROM users WHERE email = ? AND id != ? LIMIT 1',
        [email, userId]
      );

      if (existingUser) {
        return res.status(409).json({ error: 'email_already_exists' });
      }

      updates.push('email = ?');
      values.push(email);
    }

    if (password !== undefined) {
      const password_hash = await argon2.hash(password, { type: argon2.argon2id });
      updates.push('password_hash = ?');
      values.push(password_hash);
    }

    if (display_name !== undefined) {
      updates.push('display_name = ?');
      values.push(display_name);
    }

    if (updates.length > 0) {
      updates.push('updated_at = CURRENT_TIMESTAMP');
      values.push(userId);

      await connection.query(
        `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
        values
      );
    }

    // Actualizar roles si se proporcionaron
    if (roles !== undefined) {
      // Eliminar roles existentes
      await connection.query('DELETE FROM user_roles WHERE user_id = ?', [userId]);

      // Agregar nuevos roles
      for (const roleName of roles) {
        const [[role]]: any[] = await connection.query(
          'SELECT id FROM roles WHERE name = ? LIMIT 1',
          [roleName]
        );

        if (role) {
          await connection.query(
            'INSERT INTO user_roles (user_id, role_id) VALUES (?, ?)',
            [userId, role.id]
          );
        }
      }
    }

    await connection.commit();

    const updatedUser = await getUserById(userId);
    const userRoles = await getUserRoles(userId);

    res.json({
      ...updatedUser,
      password_hash: undefined,
      roles: userRoles
    });

  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
}));

// DELETE /admin/users/:id - Eliminar usuario
r.delete('/users/:id', asyncHandler(async (req, res) => {
  const userId = parseInt(req.params.id);
  const currentUserId = parseInt(req.auth!.sub);

  // No permitir que el admin se elimine a sí mismo
  if (userId === currentUserId) {
    return res.status(400).json({ error: 'cannot_delete_self' });
  }

  const user = await getUserById(userId);
  if (!user) {
    return res.status(404).json({ error: 'user_not_found' });
  }

  // Eliminar usuario (CASCADE eliminará relaciones)
  await pool.query('DELETE FROM users WHERE id = ?', [userId]);

  res.json({ message: 'user_deleted' });
}));

// ============================================
// ENDPOINTS DE ROLES
// ============================================

// GET /admin/roles - Listar todos los roles
r.get('/roles', asyncHandler(async (req, res) => {
  const [roles]: any[] = await pool.query(
    'SELECT id, name FROM roles ORDER BY name'
  );

  res.json({ roles });
}));

// ============================================
// ENDPOINTS DE CLIENTES OIDC
// ============================================

// GET /admin/clients - Listar clientes OIDC
r.get('/clients', asyncHandler(async (req, res) => {
  const [clients]: any[] = await pool.query(
    `SELECT id, client_id, name, redirect_uris, allowed_scopes,
            require_pkce, created_at
     FROM oidc_clients
     ORDER BY created_at DESC`
  );

  // Parsear redirect_uris JSON
  const parsedClients = clients.map((client: any) => ({
    ...client,
    redirect_uris: JSON.parse(client.redirect_uris),
    require_pkce: Boolean(client.require_pkce)
  }));

  res.json({ clients: parsedClients });
}));

// GET /admin/clients/:id - Obtener cliente por ID
r.get('/clients/:id', asyncHandler(async (req, res) => {
  const clientId = parseInt(req.params.id);

  const [[client]]: any[] = await pool.query(
    'SELECT * FROM oidc_clients WHERE id = ? LIMIT 1',
    [clientId]
  );

  if (!client) {
    return res.status(404).json({ error: 'client_not_found' });
  }

  res.json({
    ...client,
    redirect_uris: JSON.parse(client.redirect_uris),
    require_pkce: Boolean(client.require_pkce)
  });
}));

// POST /admin/clients - Crear nuevo cliente OIDC
const CreateClientSchema = z.object({
  client_id: z.string().min(3),
  name: z.string().optional(),
  redirect_uris: z.array(z.string().url()),
  allowed_scopes: z.string().default('openid profile email'),
  require_pkce: z.boolean().default(true)
});

r.post('/clients', asyncHandler(async (req, res) => {
  const parsed = CreateClientSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const { client_id, name, redirect_uris, allowed_scopes, require_pkce } = parsed.data;

  // Verificar que el client_id no exista
  const existingClient = await getClientById(client_id);
  if (existingClient) {
    return res.status(409).json({ error: 'client_id_already_exists' });
  }

  const [result]: any = await pool.query(
    `INSERT INTO oidc_clients (client_id, name, redirect_uris, allowed_scopes, require_pkce)
     VALUES (?, ?, ?, ?, ?)`,
    [
      client_id,
      name || null,
      JSON.stringify(redirect_uris),
      allowed_scopes,
      require_pkce
    ]
  );

  const [[newClient]]: any[] = await pool.query(
    'SELECT * FROM oidc_clients WHERE id = ? LIMIT 1',
    [result.insertId]
  );

  res.status(201).json({
    ...newClient,
    redirect_uris: JSON.parse(newClient.redirect_uris),
    require_pkce: Boolean(newClient.require_pkce)
  });
}));

// PUT /admin/clients/:id - Actualizar cliente OIDC
const UpdateClientSchema = z.object({
  name: z.string().optional(),
  redirect_uris: z.array(z.string().url()).optional(),
  allowed_scopes: z.string().optional(),
  require_pkce: z.boolean().optional()
});

r.put('/clients/:id', asyncHandler(async (req, res) => {
  const clientId = parseInt(req.params.id);
  const parsed = UpdateClientSchema.safeParse(req.body);

  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const [[existingClient]]: any[] = await pool.query(
    'SELECT id FROM oidc_clients WHERE id = ? LIMIT 1',
    [clientId]
  );

  if (!existingClient) {
    return res.status(404).json({ error: 'client_not_found' });
  }

  const { name, redirect_uris, allowed_scopes, require_pkce } = parsed.data;
  const updates: string[] = [];
  const values: any[] = [];

  if (name !== undefined) {
    updates.push('name = ?');
    values.push(name);
  }

  if (redirect_uris !== undefined) {
    updates.push('redirect_uris = ?');
    values.push(JSON.stringify(redirect_uris));
  }

  if (allowed_scopes !== undefined) {
    updates.push('allowed_scopes = ?');
    values.push(allowed_scopes);
  }

  if (require_pkce !== undefined) {
    updates.push('require_pkce = ?');
    values.push(require_pkce);
  }

  if (updates.length > 0) {
    values.push(clientId);
    await pool.query(
      `UPDATE oidc_clients SET ${updates.join(', ')} WHERE id = ?`,
      values
    );
  }

  const [[updatedClient]]: any[] = await pool.query(
    'SELECT * FROM oidc_clients WHERE id = ? LIMIT 1',
    [clientId]
  );

  res.json({
    ...updatedClient,
    redirect_uris: JSON.parse(updatedClient.redirect_uris),
    require_pkce: Boolean(updatedClient.require_pkce)
  });
}));

// DELETE /admin/clients/:id - Eliminar cliente OIDC
r.delete('/clients/:id', asyncHandler(async (req, res) => {
  const clientId = parseInt(req.params.id);

  const [[client]]: any[] = await pool.query(
    'SELECT id FROM oidc_clients WHERE id = ? LIMIT 1',
    [clientId]
  );

  if (!client) {
    return res.status(404).json({ error: 'client_not_found' });
  }

  await pool.query('DELETE FROM oidc_clients WHERE id = ?', [clientId]);

  res.json({ message: 'client_deleted' });
}));

// ============================================
// ENDPOINTS DE SISTEMA/CONFIGURACIÓN
// ============================================

// GET /admin/stats - Estadísticas del sistema
r.get('/stats', asyncHandler(async (req, res) => {
  const [[userCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM users');
  const [[clientCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM oidc_clients');
  const [[tokenCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM refresh_tokens WHERE revoked = 0');

  // Usuarios registrados en los últimos 30 días
  const [[recentUsers]]: any[] = await pool.query(
    'SELECT COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)'
  );

  res.json({
    stats: {
      total_users: userCount.count,
      total_clients: clientCount.count,
      active_tokens: tokenCount.count,
      recent_users: recentUsers.count
    }
  });
}));

// GET /admin/config - Obtener configuración actual
r.get('/config', asyncHandler(async (req, res) => {
  // En un caso real, esto podría venir de una tabla de configuración
  // Por ahora devolvemos variables de entorno
  const config = {
    oidc_issuer: process.env.OIDC_ISSUER,
    oidc_audience: process.env.OIDC_AUDIENCE,
    public_base_url: process.env.PUBLIC_BASE_URL,
    // No exponemos información sensible como DB credentials
  };

  res.json({ config });
}));

// POST /admin/jwk/rotate - Rotar claves JWK (generar nueva clave activa)
r.post('/jwk/rotate', asyncHandler(async (req, res) => {
  const { ensureActiveJwk } = await import('../crypto/jwk.js');

  // Desactivar clave actual
  await pool.query('UPDATE jwk_keys SET is_active = 0 WHERE is_active = 1');

  // Generar nueva clave activa
  const newKey = await ensureActiveJwk();

  res.json({
    message: 'jwk_rotated',
    new_kid: newKey.kid
  });
}));

// GET /admin/ping - Health check con información del usuario
r.get('/ping', asyncHandler(async (req, res) => {
  res.json({
    ok: true,
    user: req.auth?.sub,
    roles: req.userRoles,
    timestamp: new Date().toISOString()
  });
}));

export default r;
