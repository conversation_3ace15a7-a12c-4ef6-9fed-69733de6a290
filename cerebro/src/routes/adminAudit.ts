// src/routes/adminAudit.ts - Endpoints para consultar audit logs
import { Router } from 'express';
import { z } from 'zod';
import { pool } from '../db.js';
import { requireAdmin } from '../middlewares/requireAuth.js';
import { asyncHandler } from '../middlewares/errorHandler.js';

const r = Router();

// Middleware para todas las rutas de audit
r.use(requireAdmin);

// GET /admin/audit/logs - Obtener logs de auditoría con filtros
const AuditQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  user_id: z.coerce.number().optional(),
  action: z.string().optional(),
  resource_type: z.string().optional(),
  resource_id: z.string().optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  ip_address: z.string().optional()
});

r.get('/logs', asyncHandler(async (req, res) => {
  const parsed = AuditQuerySchema.safeParse(req.query);
  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const { page, limit, user_id, action, resource_type, resource_id, date_from, date_to, ip_address } = parsed.data;
  const offset = (page - 1) * limit;

  // Construir WHERE clause dinámicamente
  const conditions: string[] = [];
  const params: any[] = [];

  if (user_id) {
    conditions.push('al.user_id = ?');
    params.push(user_id);
  }

  if (action) {
    conditions.push('al.action = ?');
    params.push(action);
  }

  if (resource_type) {
    conditions.push('al.resource_type = ?');
    params.push(resource_type);
  }

  if (resource_id) {
    conditions.push('al.resource_id = ?');
    params.push(resource_id);
  }

  if (date_from) {
    conditions.push('al.created_at >= ?');
    params.push(date_from);
  }

  if (date_to) {
    conditions.push('al.created_at <= ?');
    params.push(date_to);
  }

  if (ip_address) {
    conditions.push('al.ip_address = ?');
    params.push(ip_address);
  }

  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

  // Query principal con JOIN para obtener información del usuario
  const [logs]: any[] = await pool.query(
    `SELECT
       al.*,
       u.email as user_email,
       u.display_name as user_name
     FROM audit_log al
     LEFT JOIN users u ON al.user_id = u.id
     ${whereClause}
     ORDER BY al.created_at DESC
     LIMIT ? OFFSET ?`,
    [...params, limit, offset]
  );

  // Query para contar total
  const [[countResult]]: any[] = await pool.query(
    `SELECT COUNT(*) as total FROM audit_log al ${whereClause}`,
    params
  );

  // Parsear details JSON
  const parsedLogs = logs.map((log: any) => ({
    ...log,
    details: log.details ? JSON.parse(log.details) : null
  }));

  res.json({
    logs: parsedLogs,
    pagination: {
      page,
      limit,
      total: countResult.total,
      pages: Math.ceil(countResult.total / limit)
    }
  });
}));

// GET /admin/audit/stats - Estadísticas de auditoría
r.get('/stats', asyncHandler(async (req, res) => {
  const days = parseInt(req.query.days as string) || 30;

  // Acciones más frecuentes
  const [topActions]: any[] = await pool.query(
    `SELECT action, COUNT(*) as count
     FROM audit_log
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
     GROUP BY action
     ORDER BY count DESC
     LIMIT 10`,
    [days]
  );

  // Usuarios más activos
  const [topUsers]: any[] = await pool.query(
    `SELECT
       al.user_id,
       u.email,
       u.display_name,
       COUNT(*) as action_count
     FROM audit_log al
     LEFT JOIN users u ON al.user_id = u.id
     WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
       AND al.user_id IS NOT NULL
     GROUP BY al.user_id, u.email, u.display_name
     ORDER BY action_count DESC
     LIMIT 10`,
    [days]
  );

  // Actividad por día
  const [dailyActivity]: any[] = await pool.query(
    `SELECT
       DATE(created_at) as date,
       COUNT(*) as count
     FROM audit_log
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
     GROUP BY DATE(created_at)
     ORDER BY date DESC`,
    [days]
  );

  // Tipos de recursos más modificados
  const [resourceTypes]: any[] = await pool.query(
    `SELECT
       resource_type,
       COUNT(*) as count
     FROM audit_log
     WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
     GROUP BY resource_type
     ORDER BY count DESC`,
    [days]
  );

  res.json({
    period_days: days,
    top_actions: topActions,
    top_users: topUsers,
    daily_activity: dailyActivity,
    resource_types: resourceTypes
  });
}));

// GET /admin/audit/actions - Obtener lista de acciones disponibles
r.get('/actions', asyncHandler(async (req, res) => {
  const [actions]: any[] = await pool.query(
    `SELECT DISTINCT action FROM audit_log ORDER BY action`
  );

  const [resourceTypes]: any[] = await pool.query(
    `SELECT DISTINCT resource_type FROM audit_log ORDER BY resource_type`
  );

  res.json({
    actions: actions.map((a: any) => a.action),
    resource_types: resourceTypes.map((r: any) => r.resource_type)
  });
}));

// DELETE /admin/audit/cleanup - Limpiar logs antiguos
const CleanupSchema = z.object({
  days: z.number().min(30).max(365) // Mínimo 30 días, máximo 1 año
});

r.delete('/cleanup', asyncHandler(async (req, res) => {
  const parsed = CleanupSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const { days } = parsed.data;

  // Contar logs que se van a eliminar
  const [[countResult]]: any[] = await pool.query(
    'SELECT COUNT(*) as count FROM audit_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
    [days]
  );

  // Eliminar logs antiguos
  const [result]: any = await pool.query(
    'DELETE FROM audit_log WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
    [days]
  );

  // Log de la acción de limpieza
  const { createAuditLog } = await import('../middlewares/auditLog.js');
  await createAuditLog({
    user_id: parseInt(req.auth!.sub),
    action: 'audit_cleanup',
    resource_type: 'audit_log',
    details: {
      days_threshold: days,
      deleted_count: result.affectedRows
    },
    ip_address: req.ip
  });

  res.json({
    message: 'audit_logs_cleaned',
    deleted_count: result.affectedRows,
    days_threshold: days
  });
}));

export default r;
