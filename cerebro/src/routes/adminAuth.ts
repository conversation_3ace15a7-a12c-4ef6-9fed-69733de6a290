// src/routes/adminAuth.ts
import { Router } from 'express';
import { z } from 'zod';
import { pool } from '../db.js';
import { asyncHandler } from '../middlewares/errorHandler.js';
import { getUserByEmail, verifyPassword, getUserRoles } from '../services/users.js';
import { signIdToken, ensureActiveJwk } from '../crypto/jwk.js';
import { env } from '../env.js';
import crypto from 'crypto';

const r = Router();

// POST /admin/auth/login - Login específico para admin panel
const AdminLoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1)
});

r.post('/login', asyncHandler(async (req, res) => {
  const parsed = AdminLoginSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const { email, password } = parsed.data;

  // Buscar usuario
  const user = await getUserByEmail(email);
  if (!user) {
    return res.status(401).json({ error: 'invalid_credentials' });
  }

  // Verificar contraseña
  const isValidPassword = await verifyPassword(user, password);
  if (!isValidPassword) {
    return res.status(401).json({ error: 'invalid_credentials' });
  }

  // Verificar que tenga rol ADMIN
  const userRoles = await getUserRoles(user.id);
  if (!userRoles.includes('ADMIN')) {
    return res.status(403).json({ error: 'admin_access_required' });
  }

  // Generar tokens específicos para admin
  const kidRow = await ensureActiveJwk();
  const sub = String(user.id);

  // Access token con scope admin
  const access_token = await signIdToken(
    {
      iss: env.OIDC_ISSUER,
      aud: env.OIDC_AUDIENCE,
      sub,
      scope: 'admin openid profile email',
      roles: userRoles
    },
    kidRow.kid
  );

  // Refresh token específico para admin
  const refresh_token = crypto.randomUUID();
  const exp = new Date(Date.now() + 7 * 24 * 3600 * 1000); // 7 días

  await pool.query(
    'INSERT INTO refresh_tokens (token, user_id, client_id, expires_at) VALUES (?, ?, ?, ?)',
    [refresh_token, user.id, 'admin-panel', exp]
  );

  res.json({
    access_token,
    refresh_token,
    token_type: 'Bearer',
    expires_in: 900, // 15 minutos
    user: {
      id: user.id,
      email: user.email,
      display_name: user.display_name,
      roles: userRoles
    }
  });
}));

// POST /admin/auth/refresh - Renovar token de admin
const RefreshTokenSchema = z.object({
  refresh_token: z.string()
});

r.post('/refresh', asyncHandler(async (req, res) => {
  const parsed = RefreshTokenSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: 'validation_error', details: parsed.error.issues });
  }

  const { refresh_token } = parsed.data;

  // Buscar refresh token
  const [[rt]]: any[] = await pool.query(
    'SELECT * FROM refresh_tokens WHERE token = ? AND revoked = 0 AND client_id = ? LIMIT 1',
    [refresh_token, 'admin-panel']
  );

  if (!rt) {
    return res.status(401).json({ error: 'invalid_refresh_token' });
  }

  if (new Date(rt.expires_at).getTime() < Date.now()) {
    // Limpiar token expirado
    await pool.query('UPDATE refresh_tokens SET revoked = 1 WHERE token = ?', [refresh_token]);
    return res.status(401).json({ error: 'expired_refresh_token' });
  }

  // Verificar que el usuario siga siendo admin
  const userRoles = await getUserRoles(rt.user_id);
  if (!userRoles.includes('ADMIN')) {
    // Revocar token si ya no es admin
    await pool.query('UPDATE refresh_tokens SET revoked = 1 WHERE token = ?', [refresh_token]);
    return res.status(403).json({ error: 'admin_access_revoked' });
  }

  // Generar nuevo access token
  const kidRow = await ensureActiveJwk();
  const sub = String(rt.user_id);

  const access_token = await signIdToken(
    {
      iss: env.OIDC_ISSUER,
      aud: env.OIDC_AUDIENCE,
      sub,
      scope: 'admin openid profile email',
      roles: userRoles
    },
    kidRow.kid
  );

  // Opcional: Rotar refresh token por seguridad
  const newRefreshToken = crypto.randomUUID();
  const newExp = new Date(Date.now() + 7 * 24 * 3600 * 1000);

  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();

    // Revocar el anterior
    await connection.query(
      'UPDATE refresh_tokens SET revoked = 1 WHERE token = ?',
      [refresh_token]
    );

    // Crear nuevo
    await connection.query(
      'INSERT INTO refresh_tokens (token, user_id, client_id, expires_at) VALUES (?, ?, ?, ?)',
      [newRefreshToken, rt.user_id, 'admin-panel', newExp]
    );

    await connection.commit();
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }

  res.json({
    access_token,
    refresh_token: newRefreshToken,
    token_type: 'Bearer',
    expires_in: 900
  });
}));

// POST /admin/auth/logout - Logout (revocar tokens)
r.post('/logout', asyncHandler(async (req, res) => {
  const refresh_token = req.body.refresh_token;

  if (refresh_token) {
    // Revocar el refresh token específico
    await pool.query(
      'UPDATE refresh_tokens SET revoked = 1 WHERE token = ? AND client_id = ?',
      [refresh_token, 'admin-panel']
    );
  }

  // Opcional: También podrías mantener una blacklist de access tokens
  // para invalidarlos inmediatamente, pero dado que expiran en 15 min
  // podría no ser necesario

  res.json({ message: 'logged_out' });
}));

export default r;
